package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerHistoryChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.PlanCosScaleDataDTO;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class CosModelPredictServiceImplTest {

    @InjectMocks
    private CosModelPredictServiceImpl cosModelPredictService;

    @Test
    void testBuildHistoryLineWithBigCustomerAdjustment() {
        // 准备测试数据 - 创建一个更现实的上升趋势
        List<PlanCosScaleDataDTO> data = new ArrayList<>();

        // 创建历史数据点 - 模拟一个上升趋势
        PlanCosScaleDataDTO dto1 = new PlanCosScaleDataDTO();
        dto1.setDate(LocalDate.of(2024, 1, 1));
        dto1.setValue(new BigDecimal("1000"));
        data.add(dto1);

        PlanCosScaleDataDTO dto2 = new PlanCosScaleDataDTO();
        dto2.setDate(LocalDate.of(2024, 1, 15));
        dto2.setValue(new BigDecimal("1050"));
        data.add(dto2);

        PlanCosScaleDataDTO dto3 = new PlanCosScaleDataDTO();
        dto3.setDate(LocalDate.of(2024, 2, 1));
        dto3.setValue(new BigDecimal("1100"));
        data.add(dto3);

        PlanCosScaleDataDTO dto4 = new PlanCosScaleDataDTO();
        dto4.setDate(LocalDate.of(2024, 2, 15));
        dto4.setValue(new BigDecimal("1150"));
        data.add(dto4);

        PlanCosScaleDataDTO dto5 = new PlanCosScaleDataDTO();
        dto5.setDate(LocalDate.of(2024, 3, 1));
        dto5.setValue(new BigDecimal("1200"));
        data.add(dto5);

        // 创建大客户历史变动数据 - 在1月1日到2月1日期间调减100
        List<BigCustomerHistoryChangeDTO> bigCustomerHistoryChange = new ArrayList<>();

        BigCustomerHistoryChangeDTO change1 = new BigCustomerHistoryChangeDTO();
        change1.setStartDate(LocalDate.of(2024, 1, 1));
        change1.setEndDate(LocalDate.of(2024, 2, 1));
        change1.setNetChange(new BigDecimal("-100")); // 调减100
        bigCustomerHistoryChange.add(change1);

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "buildHistoryLine", String.class, List.class, List.class);
            method.setAccessible(true);
            
            QueryPredictResultResp.Line result = (QueryPredictResultResp.Line) method.invoke(
                    cosModelPredictService, "内部", data, bigCustomerHistoryChange);
            
            // 验证结果
            assertNotNull(result);
            assertEquals("内部", result.getScope());
            assertEquals("HISTORY", result.getType());
            assertEquals(5, result.getPoints().size());
            
            // 验证调整后的数据点 - 检查中间点（2024-01-15）的调整
            List<Object> point2 = result.getPoints().get(1); // 中间点
            assertEquals(LocalDate.of(2024, 1, 15), point2.get(0));

            BigDecimal adjustedValue = (BigDecimal) point2.get(1);

            // 验证中间点的值确实被调整了（应该小于原始值1050，因为趋势被压平了）
            assertTrue(adjustedValue.compareTo(new BigDecimal("1050")) < 0,
                    "调整后的值应该小于原始值1050，实际值: " + adjustedValue);

            // 验证调整后的值应该是1000（根据趋势调整公式）
            assertEquals(0, adjustedValue.compareTo(new BigDecimal("1000")),
                    "根据趋势调整公式，中间点应该被调整为1000，实际值: " + adjustedValue);
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testBuildHistoryLineWithoutBigCustomerAdjustment() {
        // 准备测试数据
        List<PlanCosScaleDataDTO> data = new ArrayList<>();
        
        PlanCosScaleDataDTO dto1 = new PlanCosScaleDataDTO();
        dto1.setDate(LocalDate.of(2024, 1, 1));
        dto1.setValue(new BigDecimal("1000"));
        data.add(dto1);
        
        PlanCosScaleDataDTO dto2 = new PlanCosScaleDataDTO();
        dto2.setDate(LocalDate.of(2024, 2, 1));
        dto2.setValue(new BigDecimal("1100"));
        data.add(dto2);

        // 使用反射调用私有方法进行测试（不带大客户调整的版本）
        try {
            java.lang.reflect.Method method = CosModelPredictServiceImpl.class.getDeclaredMethod(
                    "buildHistoryLine", String.class, List.class);
            method.setAccessible(true);
            
            QueryPredictResultResp.Line result = (QueryPredictResultResp.Line) method.invoke(
                    cosModelPredictService, "外部", data);
            
            // 验证结果
            assertNotNull(result);
            assertEquals("外部", result.getScope());
            assertEquals("HISTORY", result.getType());
            assertEquals(2, result.getPoints().size());
            
            // 验证数据点没有被调整
            List<Object> point1 = result.getPoints().get(0);
            assertEquals(LocalDate.of(2024, 1, 1), point1.get(0));
            assertEquals(new BigDecimal("1000"), point1.get(1));
            
            List<Object> point2 = result.getPoints().get(1);
            assertEquals(LocalDate.of(2024, 2, 1), point2.get(0));
            assertEquals(new BigDecimal("1100"), point2.get(1));
            
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }
}
